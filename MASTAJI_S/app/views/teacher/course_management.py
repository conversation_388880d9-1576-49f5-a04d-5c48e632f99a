"""
课程管理相关路由
"""
from flask import request, jsonify, session
from app.models.database import get_db
from app.utils.decorators import teacher_required
from datetime import datetime
import uuid
import json


def register_routes(bp):
    """注册课程管理相关路由"""
    
    @bp.route('/start_class/<course_schedule_id>', methods=['POST'])
    @teacher_required
    def start_class(course_schedule_id):
        """开始上课"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证课程安排是否属于当前教师
            cursor.execute("""
                SELECT id, status FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            course = cursor.fetchone()
            if not course:
                conn.close()
                return jsonify({"status": "error", "message": "课程不存在或无权限"}), 403

            if course['status'] == 'in_progress':
                conn.close()
                return jsonify({"status": "error", "message": "课程已经在进行中"}), 400

            # 结束其他正在进行的课程
            cursor.execute("""
                UPDATE course_schedules
                SET status = 'completed', end_datetime = ?
                WHERE teacher_id = ? AND status = 'in_progress'
            """, (datetime.now().isoformat(), session.get('teacher_id')))

            # 开始当前课程
            cursor.execute("""
                UPDATE course_schedules
                SET status = 'in_progress', start_datetime = ?
                WHERE id = ?
            """, (datetime.now().isoformat(), course_schedule_id))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "课程已开始"})

        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    @bp.route('/end_class/<course_schedule_id>', methods=['POST'])
    @teacher_required
    def end_class(course_schedule_id):
        """结束上课"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证课程安排是否属于当前教师
            cursor.execute("""
                SELECT id, status FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            course = cursor.fetchone()
            if not course:
                conn.close()
                return jsonify({"status": "error", "message": "课程不存在或无权限"}), 403

            if course['status'] != 'in_progress':
                conn.close()
                return jsonify({"status": "error", "message": "课程未在进行中"}), 400

            # 结束课程
            cursor.execute("""
                UPDATE course_schedules
                SET status = 'completed', end_datetime = ?
                WHERE id = ?
            """, (datetime.now().isoformat(), course_schedule_id))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "课程已结束"})

        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    @bp.route('/get_course_schedules', methods=['GET'])
    @teacher_required
    def get_course_schedules():
        """获取当前教师的课程安排列表（API）"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取当前教师的所有课程安排
            cursor.execute("""
                SELECT cs.id, c.name as course_name, c.code as course_code,
                       cl.name as classroom_name, cls.name as class_name,
                       cs.day_of_week, cs.start_time, cs.end_time, cs.status,
                       cs.start_datetime, cs.description
                FROM course_schedules cs
                JOIN courses c ON cs.course_id = c.id
                JOIN classrooms cl ON cs.classroom_id = cl.id
                JOIN classes cls ON cs.class_id = cls.id
                WHERE cs.teacher_id = ?
                ORDER BY
                    CASE cs.day_of_week
                        WHEN '星期一' THEN 1
                        WHEN '星期二' THEN 2
                        WHEN '星期三' THEN 3
                        WHEN '星期四' THEN 4
                        WHEN '星期五' THEN 5
                        WHEN '星期六' THEN 6
                        WHEN '星期日' THEN 7
                    END,
                    cs.start_time
            """, (session.get('teacher_id'),))

            course_schedules = cursor.fetchall()

            # 获取当前选中的课堂ID
            current_class_id = session.get('current_class_id')

            conn.close()

            # 转换为字典列表
            schedules_list = [dict(schedule) for schedule in course_schedules]

            return jsonify({
                "status": "success",
                "course_schedules": schedules_list,
                "current_class_id": current_class_id
            })

        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    @bp.route('/get_course_schedule/<course_schedule_id>', methods=['GET'])
    @teacher_required
    def get_course_schedule(course_schedule_id):
        """获取课程安排详情"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取课程安排详情，确保属于当前教师
            cursor.execute("""
                SELECT cs.id, cs.course_id, cs.classroom_id, cs.class_id,
                       cs.day_of_week, cs.start_time, cs.end_time, cs.description,
                       c.name as course_name, cl.name as classroom_name, cls.name as class_name
                FROM course_schedules cs
                JOIN courses c ON cs.course_id = c.id
                JOIN classrooms cl ON cs.classroom_id = cl.id
                JOIN classes cls ON cs.class_id = cls.id
                WHERE cs.id = ? AND cs.teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            course_schedule = cursor.fetchone()
            if not course_schedule:
                conn.close()
                return jsonify({"status": "error", "message": "课程安排不存在或无权限"}), 404

            conn.close()

            # 转换为字典格式
            data = {
                'id': course_schedule['id'],
                'course_id': course_schedule['course_id'],
                'classroom_id': course_schedule['classroom_id'],
                'class_id': course_schedule['class_id'],
                'day_of_week': course_schedule['day_of_week'],
                'start_time': course_schedule['start_time'],
                'end_time': course_schedule['end_time'],
                'description': course_schedule['description'] or '',
                'course_name': course_schedule['course_name'],
                'classroom_name': course_schedule['classroom_name'],
                'class_name': course_schedule['class_name']
            }

            return jsonify({"status": "success", "data": data})

        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    @bp.route('/add_course_schedule', methods=['POST'])
    @teacher_required
    def add_course_schedule():
        """添加课程安排"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"status": "error", "message": "无效的请求数据"}), 400

            # 验证必要字段
            required_fields = ['course_id', 'classroom_id', 'class_id', 'day_of_week', 'start_time', 'end_time']
            for field in required_fields:
                if field not in data or not data[field]:
                    return jsonify({"status": "error", "message": f"缺少必要字段: {field}"}), 400

            conn = get_db()
            cursor = conn.cursor()

            # 检查时间冲突
            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE teacher_id = ? AND day_of_week = ?
                AND ((start_time <= ? AND end_time > ?) OR (start_time < ? AND end_time >= ?))
            """, (session.get('teacher_id'), data['day_of_week'],
                  data['start_time'], data['start_time'], data['end_time'], data['end_time']))

            if cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "该时间段已有课程安排"}), 400

            # 生成课程安排ID
            schedule_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()

            # 插入课程安排
            cursor.execute("""
                INSERT INTO course_schedules (
                    id, teacher_id, course_id, classroom_id, class_id,
                    day_of_week, start_time, end_time, description, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                schedule_id,
                session.get('teacher_id'),
                data['course_id'],
                data['classroom_id'],
                data['class_id'],
                data['day_of_week'],
                data['start_time'],
                data['end_time'],
                data.get('description', ''),
                'scheduled',
                current_time
            ))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "课程安排添加成功", "schedule_id": schedule_id})

        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    @bp.route('/update_course_schedule/<course_schedule_id>', methods=['PUT'])
    @teacher_required
    def update_course_schedule(course_schedule_id):
        """更新课程安排"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"status": "error", "message": "无效的请求数据"}), 400

            # 验证必要字段
            required_fields = ['course_id', 'classroom_id', 'class_id', 'day_of_week', 'start_time', 'end_time']
            for field in required_fields:
                if field not in data or not data[field]:
                    return jsonify({"status": "error", "message": f"缺少必要字段: {field}"}), 400

            conn = get_db()
            cursor = conn.cursor()

            # 验证课程安排是否属于当前教师
            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "课程安排不存在或无权限"}), 404

            # 检查时间冲突（排除当前课程安排）
            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE teacher_id = ? AND day_of_week = ? AND id != ?
                AND ((start_time <= ? AND end_time > ?) OR (start_time < ? AND end_time >= ?))
            """, (session.get('teacher_id'), data['day_of_week'], course_schedule_id,
                  data['start_time'], data['start_time'], data['end_time'], data['end_time']))

            if cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "该时间段已有课程安排"}), 400

            # 更新课程安排
            cursor.execute("""
                UPDATE course_schedules SET
                    course_id = ?, classroom_id = ?, class_id = ?,
                    day_of_week = ?, start_time = ?, end_time = ?, description = ?
                WHERE id = ?
            """, (
                data['course_id'],
                data['classroom_id'],
                data['class_id'],
                data['day_of_week'],
                data['start_time'],
                data['end_time'],
                data.get('description', ''),
                course_schedule_id
            ))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "课程安排更新成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    @bp.route('/delete_course_schedule/<course_schedule_id>', methods=['DELETE'])
    @teacher_required
    def delete_course_schedule(course_schedule_id):
        """删除课程安排"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证课程安排是否属于当前教师
            cursor.execute("""
                SELECT id, status FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            course = cursor.fetchone()
            if not course:
                conn.close()
                return jsonify({"status": "error", "message": "课程安排不存在或无权限"}), 404

            # 检查是否正在进行中
            if course['status'] == 'in_progress':
                conn.close()
                return jsonify({"status": "error", "message": "无法删除正在进行中的课程"}), 400

            # 删除相关的考勤记录
            cursor.execute("DELETE FROM class_attendance WHERE course_schedule_id = ?", (course_schedule_id,))

            # 删除相关的考试
            cursor.execute("DELETE FROM exams WHERE course_schedule_id = ?", (course_schedule_id,))

            # 删除课程安排
            cursor.execute("DELETE FROM course_schedules WHERE id = ?", (course_schedule_id,))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "课程安排删除成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    @bp.route('/class/<course_schedule_id>/students', methods=['GET'])
    @teacher_required
    def get_class_students(course_schedule_id):
        """获取指定课堂的学生列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取该班级的所有学生
            cursor.execute("""
                SELECT s.student_id, s.name
                FROM students s
                JOIN course_schedules cs ON s.class_id = cs.class_id
                WHERE cs.id = ?
                ORDER BY s.name
            """, (course_schedule_id,))

            students = [dict(row) for row in cursor.fetchall()]
            conn.close()

            return jsonify({"success": True, "students": students})

        except Exception as e:
            return jsonify({"success": False, "message": str(e)}), 500

    @bp.route('/class/<course_schedule_id>/students/random', methods=['GET'])
    @teacher_required
    def get_random_student(course_schedule_id):
        """随机选择一个学生"""
        try:
            import random

            conn = get_db()
            cursor = conn.cursor()

            # 获取该班级的所有学生
            cursor.execute("""
                SELECT s.student_id, s.name, s.gender
                FROM students s
                JOIN course_schedules cs ON s.class_id = cs.class_id
                WHERE cs.id = ?
                ORDER BY s.name
            """, (course_schedule_id,))

            students = [dict(row) for row in cursor.fetchall()]
            conn.close()

            if not students:
                return jsonify({"success": False, "message": "该课堂没有学生"}), 404

            # 随机选择一个学生
            selected_student = random.choice(students)

            return jsonify({
                "success": True,
                "student": {
                    "id": selected_student["student_id"],
                    "name": selected_student["name"],
                    "gender": selected_student["gender"],
                    "avatar": f"default_{selected_student['gender']}.png"  # 默认头像
                }
            })

        except Exception as e:
            return jsonify({"success": False, "message": str(e)}), 500

    @bp.route('/class/<course_schedule_id>/students/for_picker', methods=['GET'])
    @teacher_required
    def get_students_for_picker(course_schedule_id):
        """获取用于选人工具的学生列表（兼容原JSON格式）"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取该班级的所有学生
            cursor.execute("""
                SELECT s.student_id, s.name, s.gender
                FROM students s
                JOIN course_schedules cs ON s.class_id = cs.class_id
                WHERE cs.id = ?
                ORDER BY s.name
            """, (course_schedule_id,))

            students = cursor.fetchall()
            conn.close()

            # 转换为兼容原JSON格式的数据
            students_data = []
            for student in students:
                students_data.append({
                    "id": student["student_id"],
                    "name": student["name"],
                    "avatar": f"default_{student['gender']}.png"  # 使用默认头像
                })

            return jsonify({"success": True, "students": students_data})

        except Exception as e:
            return jsonify({"success": False, "message": str(e)}), 500

