#!/bin/bash

# 小马助教启动脚本
# 解决Qt平台插件问题，无需修改系统Qt环境

echo "正在启动小马助教..."

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 激活虚拟环境
source venv/bin/activate

# 设置Qt相关环境变量
export QT_QPA_PLATFORM=xcb
export QT_QPA_PLATFORM_PLUGIN_PATH="$SCRIPT_DIR/venv/lib/python3.13/site-packages/PyQt5/Qt5/plugins"

# 设置QtWebEngine环境变量
export QTWEBENGINE_DISABLE_SANDBOX=1
export QTWEBENGINE_CHROMIUM_FLAGS="--no-sandbox --disable-dev-shm-usage --disable-gpu --disable-software-rasterizer --disable-extensions --disable-plugins"

# 设置显示相关环境变量
export DISPLAY=${DISPLAY:-:0}

# 检查必要的系统库
echo "检查系统依赖..."

# 检查xcb库
if ! ldconfig -p | grep -q libxcb; then
    echo "警告: 缺少libxcb库，可能需要安装 libxcb1-dev"
fi

# 检查X11库
if ! ldconfig -p | grep -q libX11; then
    echo "警告: 缺少libX11库，可能需要安装 libx11-dev"
fi

# 尝试启动程序
echo "启动程序..."
python Mastaji.py

echo "程序已退出"
