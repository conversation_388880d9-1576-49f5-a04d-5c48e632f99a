# 选人功能数据库连接说明

## 概述

本文档说明了如何将MASTAJI_A中的选人功能与MASTAJI_S后端数据库进行连接，实现从JSON文件到数据库的数据源切换。

## 修改内容

### 1. MASTAJI_S后端修改

在 `MASTAJI_S/app/views/teacher/course_management.py` 中添加了以下新的API接口：

#### 1.1 随机选择学生接口
```
GET /teacher/class/<course_schedule_id>/students/random
```
- 功能：从指定课堂随机选择一个学生
- 返回：学生的基本信息（ID、姓名、性别）

#### 1.2 获取选人工具学生列表接口
```
GET /teacher/class/<course_schedule_id>/students/for_picker
```
- 功能：获取指定课堂的所有学生，格式兼容原JSON结构
- 返回：学生列表，包含ID、姓名、默认头像

### 2. MASTAJI_A前端修改

#### 2.1 新增功能
- 添加了课程选择下拉框
- 支持从后端服务器获取课程列表和学生数据
- 添加了网络请求错误处理
- 保留了本地JSON文件作为备用方案

#### 2.2 界面改进
- 增加了课程选择区域
- 优化了界面布局和样式
- 添加了刷新按钮
- 改进了头像显示逻辑

#### 2.3 配置文件
新增 `config.json` 配置文件，包含：
- 服务器地址配置
- 默认教师ID
- 界面尺寸配置

## 使用方法

### 1. 启动后端服务
```bash
cd MASTAJI_S
python run.py
```
默认服务地址：http://localhost:8080

### 2. 配置前端
编辑 `MASTAJI_A/config.json` 文件：
```json
{
    "server": {
        "url": "http://localhost:8080",
        "timeout": 5
    },
    "teacher": {
        "default_id": "T001",
        "default_password": "123"
    },
    "ui": {
        "window_width": 450,
        "window_height": 500,
        "avatar_size": 150
    }
}
```

**注意**: 默认教师密码是 `123`，不是 `123456`。

### 3. 测试连接
```bash
cd MASTAJI_A
python test_picker_cli.py
```

### 4. 启动选人工具
```bash
cd MASTAJI_A
python Select_People.py
```

**注意**: 在无图形界面环境中，PyQt5应用无法运行。可以使用 `test_picker_cli.py` 进行功能测试。

### 5. 使用流程
1. 启动选人工具后，会自动加载课程列表
2. 从下拉框中选择要进行选人的课程
3. 系统会自动加载该课程的学生列表
4. 点击"开始随机选人"进行选人
5. 点击"停止"确定选中的学生

## 错误处理

### 1. 网络连接失败
- 如果无法连接到后端服务器，系统会自动切换到本地JSON文件模式
- 确保 `database/students.json` 文件存在作为备用

### 2. 课程数据为空
- 如果选择的课程没有学生，会显示相应提示
- 可以点击"刷新课程列表"重新加载

### 3. 服务器错误
- 系统会显示具体的错误信息
- 检查后端服务是否正常运行
- 检查教师ID是否正确

## 技术细节

### 1. 数据流向
```
MASTAJI_A (选人工具) 
    ↓ HTTP请求
MASTAJI_S (后端API) 
    ↓ SQL查询
SQLite数据库 (学生数据)
```

### 2. API认证
- 目前使用Cookie方式传递教师ID
- 后续可以集成完整的登录认证系统

### 3. 数据格式兼容
- 保持了与原JSON格式的兼容性
- 支持平滑迁移，无需修改其他相关代码

## 扩展功能

### 1. 可以添加的功能
- 学生头像上传和显示
- 选人历史记录
- 按条件筛选学生（如性别、成绩等）
- 分组选人功能

### 2. 性能优化
- 添加数据缓存机制
- 实现增量数据更新
- 优化网络请求频率

## 注意事项

1. 确保MASTAJI_S服务正常运行
2. 检查网络连接和防火墙设置
3. 确保数据库中有相应的课程和学生数据
4. 定期备份数据库文件
