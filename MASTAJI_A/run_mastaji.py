#!/usr/bin/env python3
"""
小马助教启动器
解决Qt平台插件问题的Python启动脚本
"""

import os
import sys
import subprocess

def setup_qt_environment():
    """设置Qt环境变量"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 设置Qt插件路径
    plugin_path = os.path.join(script_dir, 'venv', 'lib', 'python3.13', 'site-packages', 'PyQt5', 'Qt5', 'plugins')
    os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = plugin_path
    os.environ['QT_QPA_PLATFORM'] = 'xcb'
    
    # 设置显示环境
    if 'DISPLAY' not in os.environ:
        os.environ['DISPLAY'] = ':0'
    
    # 设置Qt调试（可选，用于调试）
    # os.environ['QT_DEBUG_PLUGINS'] = '1'
    
    print(f"Qt插件路径: {plugin_path}")
    print(f"显示设备: {os.environ.get('DISPLAY', '未设置')}")

def check_dependencies():
    """检查必要的依赖"""
    print("检查系统依赖...")
    
    # 检查虚拟环境
    venv_path = os.path.join(os.path.dirname(__file__), 'venv')
    if not os.path.exists(venv_path):
        print("错误: 未找到虚拟环境，请确保venv文件夹存在")
        return False
    
    # 检查PyQt5插件
    plugin_path = os.path.join(venv_path, 'lib', 'python3.13', 'site-packages', 'PyQt5', 'Qt5', 'plugins', 'platforms', 'libqxcb.so')
    if not os.path.exists(plugin_path):
        print(f"错误: 未找到xcb插件: {plugin_path}")
        return False
    
    print("依赖检查通过")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("小马助教启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 设置环境
    setup_qt_environment()
    
    # 激活虚拟环境并启动程序
    script_dir = os.path.dirname(os.path.abspath(__file__))
    venv_python = os.path.join(script_dir, 'venv', 'bin', 'python')
    mastaji_script = os.path.join(script_dir, 'Mastaji.py')
    
    print("启动小马助教...")
    try:
        # 使用虚拟环境的Python运行程序
        subprocess.run([venv_python, mastaji_script], cwd=script_dir)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
