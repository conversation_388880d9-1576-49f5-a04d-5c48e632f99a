#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小马助教系统主启动文件
"""

import os
import sys
from PyQt5.QtWidgets import QApplication, QDialog
from PyQt5.QtCore import Qt

# 添加当前目录到Python路径，确保能够导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from login_window import LoginWindow
from Mastaji import MainWindow


def main():
    """主启动函数"""
    # 高DPI支持（必须在创建QApplication之前设置）
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # 创建应用程序实例
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("小马助教系统")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("小马助教")
    
    # 显示登录窗口
    login_window = LoginWindow()
    teacher_info = None

    def on_login_success(info):
        """登录成功回调函数"""
        nonlocal teacher_info
        teacher_info = info

    # 连接登录成功信号
    login_window.login_success.connect(on_login_success)

    # 显示登录窗口并等待用户操作
    login_result = login_window.exec_()
    
    # 检查登录结果
    if login_result == QDialog.Accepted and teacher_info:
        # 登录成功，创建并显示主窗口
        main_window = MainWindow(teacher_info)
        main_window.show()
        
        # 进入应用程序主循环
        return app.exec_()
    else:
        # 登录失败或用户取消，退出程序
        return 0


if __name__ == "__main__":
    # 设置工作目录为脚本所在目录
    os.chdir(current_dir)
    
    # 启动应用程序
    exit_code = main()
    sys.exit(exit_code)
