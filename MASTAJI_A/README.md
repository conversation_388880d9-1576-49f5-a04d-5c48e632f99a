# 小马助教系统

## 项目重构说明

本项目已完成重构，将启动逻辑从 `Mastaji.py` 移动到了新的 `main.py` 文件中。

### 文件结构

- **main.py** - 主启动文件，负责应用程序的初始化和启动流程
- **Mastaji.py** - 主窗口类定义，包含所有功能模块
- **login_window.py** - 登录窗口实现
- **start.sh** - Linux/macOS 启动脚本
- **config.json** - 配置文件

### 启动方式

#### 方式一：直接运行Python文件
```bash
python main.py
```

#### 方式二：使用启动脚本（推荐）
```bash
./start.sh
```

### 启动流程

1. **初始化应用程序** - 设置高DPI支持和应用程序信息
2. **显示登录窗口** - 弹出登录界面，用户输入教师ID和密码
3. **验证登录信息** - 连接服务器验证用户身份
4. **启动主窗口** - 登录成功后显示主功能界面

### 主要功能

- 电子批注
- 屏幕投放
- 屏幕广播
- 多屏互动
- 弹幕功能
- 随机选人
- 远程控制
- 文件分享
- 资源平台
- 系统注册

### 配置说明

编辑 `config.json` 文件可以修改：
- 服务器地址和超时设置
- 保存的登录信息

### 开发说明

- **main.py** 负责应用程序的启动和初始化
- **Mastaji.py** 包含主窗口类 `MainWindow`，不再包含启动逻辑
- 所有功能模块通过主窗口进行管理和调用

### 注意事项

1. 确保已安装所有依赖包（见 requirements.txt）
2. 确保配置文件 config.json 中的服务器地址正确
3. 首次运行可能需要注册或使用试用期

### 故障排除

如果遇到启动问题：
1. 检查Python环境是否正确
2. 确认所有依赖包已安装
3. 检查配置文件格式是否正确
4. 查看终端输出的错误信息
